'use client';

import React, { useState, useEffect } from 'react';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Button } from '@/components/atoms/Button/Button';
import { getExaminationFormatAction } from '@/actions/examinationFormat.action';
import {  Calendar, FileText, User, Loader2, RefreshCw, CheckCircle, AlertTriangle} from 'lucide-react';
import { ExaminationFormatDialog } from './ExaminationFormatDialog';
import { ExaminationFormatUploadModal } from './ExaminationFormatUploadModal';

interface ExaminationFormatViewerProps {
  schoolId: string;
  schoolName?: string;
  refreshTrigger?: number; // Increment this to trigger a refresh
  className?: string;
  metadata?: {
    uploadedAt?: string;
    fileSize?: number;
    uploader?: string;
  };
  onReplaceClick?: () => void;
  onDeleteClick?: () => void;
}

export const ExaminationFormatViewer: React.FC<ExaminationFormatViewerProps> = ({
  schoolId,
  schoolName,
  refreshTrigger = 0,
  className,
  metadata,
  onReplaceClick,
  onDeleteClick,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formatData, setFormatData] = useState<{
    text?: string;
    url?: string;
    contentType?: string;
  } | null>(null);
  const [isPdfLoading, setIsPdfLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [validationStatus, setValidationStatus] = useState<'valid' | 'warning' | 'unknown'>('unknown');

  const fetchFormat = async () => {
    if (!schoolId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await getExaminationFormatAction(schoolId);
      
      if (response.status === 'success') {
        setFormatData(response.data);
        // Simulate format validation check
        setValidationStatus(Math.random() > 0.3 ? 'valid' : 'warning');
      } else {
        setError(typeof response.message === 'string' ? response.message : 'Failed to load examination format');
        setFormatData(null);
        setValidationStatus('unknown');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      setFormatData(null);
      setValidationStatus('unknown');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFormat();
  }, [schoolId, refreshTrigger]);

  const handlePdfLoad = () => {
    setIsPdfLoading(false);
  };

  const handlePdfError = () => {
    setIsPdfLoading(false);
    setError('Failed to load PDF preview');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown';
    if (bytes < 1024) return `${bytes} bytes`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  };

  // Determine if the URL is a data URL (base64 encoded)
  const isDataUrl = formatData?.url?.startsWith('data:');

  const openDialog = () => {
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
  };

  const openUploadModal = () => {
    setIsUploadModalOpen(true);
  };

  const closeUploadModal = () => {
    setIsUploadModalOpen(false);
  };

  const handleUploadSuccess = (response: any) => {
    // Refresh the format data after successful upload
    fetchFormat();
    if (onReplaceClick) {
      onReplaceClick();
    }
  };

  const getValidationStatusIcon = () => {
    switch (validationStatus) {
      case 'valid':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'warning':
        return <AlertTriangle size={16} className="text-amber-500" />;
      default:
        return null;
    }
  };

  const getValidationStatusText = () => {
    switch (validationStatus) {
      case 'valid':
        return <span className="text-green-600 text-xs font-medium">Compatible format</span>;
      case 'warning':
        return <span className="text-amber-600 text-xs font-medium">Format needs review</span>;
      default:
        return null;
    }
  };

  const handleDownload = () => {
    if (formatData?.url) {
      const link = document.createElement('a');
      link.href = formatData.url;
      link.download = 'examination_format.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <>
      <div className={`${className || ''}`} role="region" aria-label="Examination Format Viewer">
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
          {/* Content */}
          {isLoading ? (
            <div className="flex items-center justify-center p-12">
              <Loader2 size={32} className="animate-spin text-blue-600" />
            </div>
          ) :!formatData || error ? (
            <div className="flex flex-col items-center justify-center p-12 text-center">
              <div className="w-20 h-24 bg-gray-50 border border-gray-200 rounded-md flex items-center justify-center mb-4">
                <FileText size={32} className="text-gray-300" />
              </div>
              <p className="text-gray-700 font-medium mb-2">No format available</p>
              <p className="text-sm text-gray-500 mb-4">Upload an examination format for this school</p>
              <Button
                variant="primary"
                onClick={openUploadModal}
                iconProps={{
                  variant: 'upload',
                  className: 'w-4'
                }}
                className="mt-2 w-fit"
              >
                Upload Format
              </Button>
            </div>
          ) : (
            <div className="p-6">
              <div className="flex flex-col md:flex-row gap-6">
                {/* Preview Thumbnail */}
                <div 
                  className="w-full md:w-1/3 bg-gray-50 p-6 rounded-lg border border-gray-100 flex items-center justify-center cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors"
                  onClick={openDialog}
                  role="button"
                  aria-label="View examination format"
                  tabIndex={0}
                >
                  <div className="relative w-full h-full min-h-[160px] flex items-center justify-center">
                    <div className="w-24 h-32 bg-white border border-gray-200 rounded-md shadow-sm flex items-center justify-center">
                      <FileText size={32} className="text-blue-500" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 transform translate-x-1/4 translate-y-1/4">
                      {validationStatus === 'valid' && (
                        <div className="text-green-500 bg-white p-1 rounded-full border border-green-100 shadow-sm">
                          <CheckCircle size={16} />
                        </div>
                      )}
                      {validationStatus === 'warning' && (
                        <div className="text-amber-500 bg-white p-1 rounded-full border border-amber-100 shadow-sm">
                          <AlertTriangle size={16} />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Format Details */}
                <div className="w-full md:w-2/3">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="font-medium text-gray-800">{schoolName ? `${schoolName} Format` : 'Examination Format'}</h4>
                      {getValidationStatusText()}
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        onClick={openDialog}
                        iconProps={{
                          variant: 'eye',
                          className:'w-5'
                        }}
                        className="!w-auto text-xs"
                      >
                        View
                      </Button>
                      {isDataUrl && (
                        <Button
                          variant="outline"
                          onClick={handleDownload}
                          iconProps={{
                            variant: 'download',
                             className:'w-5'
                          }}
                          className="!w-auto text-xs"
                        >
                          Download
                        </Button>
                      )}
                      <Button
                        variant="error"
                        onClick={onDeleteClick}
                        iconProps={{
                          variant: 'trash-2',
                           className:'w-4'
                        }}
                        className="!w-auto text-white text-xs"
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-4">
                    {metadata?.uploadedAt && (
                      <div className="flex items-center">
                        <Calendar className="text-gray-400 mr-2 h-4 w-4" />
                        <div>
                          <span className="text-gray-500">Uploaded:</span>{' '}
                          <span className="text-gray-700">{formatDate(metadata.uploadedAt)}</span>
                        </div>
                      </div>
                    )}
                    {metadata?.fileSize && (
                      <div className="flex items-center">
                        <FileText className="text-gray-400 mr-2 h-4 w-4" />
                        <div>
                          <span className="text-gray-500">Size:</span>{' '}
                          <span className="text-gray-700">{formatFileSize(metadata.fileSize)}</span>
                        </div>
                      </div>
                    )}
                    {metadata?.uploader && (
                      <div className="flex items-center">
                        <User className="text-gray-400 mr-2 h-4 w-4" />
                        <div>
                          <span className="text-gray-500">By:</span>{' '}
                          <span className="text-gray-700">{metadata.uploader}</span>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <Button
                    variant="ghost"
                    onClick={openUploadModal}
                    iconProps={{
                      variant: 'upload',
                      className:'w-4'
                    }}
                    className="text-blue-600 w-fit hover:text-blue-800 hover:bg-blue-50 text-sm font-medium"
                  >
                    Replace Format
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Format Dialog */}
      <ExaminationFormatDialog
        isOpen={isDialogOpen}
        onClose={closeDialog}
        format={JSON.parse(formatData?.text || '{}')}
      />

      {/* Upload Modal */}
      <ExaminationFormatUploadModal
        isOpen={isUploadModalOpen}
        onClose={closeUploadModal}
        schoolId={schoolId}
        onUploadSuccess={handleUploadSuccess}
        isReplacement={!!formatData}
      />
    </>
  );
};