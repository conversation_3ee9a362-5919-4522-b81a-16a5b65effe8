'use client';

import React, { useState } from 'react';
import { ExaminationFormatUploader } from './ExaminationFormatUploader';
import { ExaminationFormatViewer } from './ExaminationFormatViewer';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Button } from '@/components/atoms/Button/Button';
import { X } from 'lucide-react';
import { DeleteExaminationFormatModal } from './DeleteExaminationFormatModal/DeleteExaminationFormatModal';

interface ExaminationFormatManagerProps {
  schoolId?: string;
  schoolName?: string;
  onDelete?: () => Promise<void>;
  className?: string;
  metadata?: {
    uploadedAt?: string;
    uploader?: string;
    fileSize?: number;
  };
  refreshTrigger?: number;
}

export const ExaminationFormatManager: React.FC<ExaminationFormatManagerProps> = ({
  schoolId,
  schoolName,
  onDelete,
  className,
  metadata,
  refreshTrigger,
}) => {
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showUploader, setShowUploader] = useState(false);

  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleDeleteSuccess = () => {
    setSuccess('Examination format deleted successfully');
  };

  const handleUploadSuccess = (data: any) => {
    setSuccess('Examination format uploaded successfully');
    setShowUploader(false); // Hide uploader after successful upload
  };

  const handleUploadError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handleReplaceClick = () => {
    setShowUploader(true);
    setError(null);
    setSuccess(null);
  };

  return (
    <div className={`${className || ''}`} role="region" aria-label="Examination Format Manager">
      {/* Error/Success Messages */}
      {error && (
        <AlertMessage 
          type="error" 
          message={error} 
          aria-live="assertive"
        />
      )}
      {success && (
        <AlertMessage 
          type="success" 
          message={success} 
          aria-live="polite"
        />
      )}

      {/* Format Viewer */}
      <div className="mb-6">
        <ExaminationFormatViewer
          schoolId={schoolId || ''}
          schoolName={schoolName}
          onReplaceClick={handleReplaceClick}
          onDeleteClick={handleDeleteClick}
          metadata={metadata}
          refreshTrigger={refreshTrigger}
        />
      </div>

      {/* Conditional Uploader for Replacement */}
      {showUploader && (
        <div 
          className="bg-white rounded-lg shadow-sm p-6 mb-6 border border-gray-200"
          role="region"
          aria-label="Replace Examination Format"
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold" id="replace-format-heading">Replace Examination Format</h3>
            <button 
              onClick={() => setShowUploader(false)}
              className="text-gray-400 hover:text-gray-600 rounded-full p-1 hover:bg-gray-100"
              aria-label="Close uploader"
              type="button"
            >
              <X className="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
          <ExaminationFormatUploader 
            schoolId={schoolId}
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
            isReplacement={true}
          />
        </div>
      )}


      {/* Delete Modal */}
      <DeleteExaminationFormatModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onDelete={onDelete}
        onSuccess={handleDeleteSuccess}
        schoolName={schoolName}
      />
    </div>
  );
};