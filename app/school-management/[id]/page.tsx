'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useParams, useRouter } from 'next/navigation'; // useParams to get ID from URL

import DashboardTemplate from '@/components/templates/Dashboard/Dashboard';
import { DetailTemplate } from '@/components/templates/DetailTemplate/DetailTemplate';
import { ListingHeader } from '@/components/molecules/ListingHeader/ListingHeader';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Button } from '@/components/atoms/Button/Button';

import { SchoolDetailCard } from '@/components/organisms/SchoolDetailCard/SchoolDetailCard';
import { SchoolModal } from '@/components/organisms/SchoolModal/SchoolModal';

import { 
  ArrowLeft, 
  FileText, 
  Info 
} from 'lucide-react';

// Actions
import { 
  handleGetSchoolByIdAction, // Use the correct action for fetching school details
  handleDeleteSchoolAction // Add delete school action
} from '@/actions/school.action';
import { ExaminationFormatManager } from '@/components/organisms/ExaminationFormatManager/ExaminationFormatManager';
import { TTransformResponse } from '@/apis/transformResponse';
import { ISchoolResponse } from '@/apis/schoolApi'; // Import ISchoolResponse
import { EUserRole } from '@/config/enums/user'; // Import EUserRole for permission check
import { deleteExaminationFormatAction } from '@/actions/examinationFormat.action';


// Removed interfaces as they're now handled by the ExaminationFormatManager component

export default function SchoolDetailPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const schoolId = params?.id as string; // Get school ID from URL

  const [schoolDetails, setSchoolDetails] = useState<ISchoolResponse | null>(null); // Use ISchoolResponse
  const [isLoadingSchool, setIsLoadingSchool] = useState<boolean>(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false); // State for edit modal
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // State for API messages
  const [apiMessage, setApiMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  // Fetch school details
  const fetchSchoolDetails = useCallback(async () => {
    if (!schoolId || status !== 'authenticated') return;
    setIsLoadingSchool(true);
    setApiMessage(null); // Clear previous messages
    try {
      const response: TTransformResponse<ISchoolResponse> = await handleGetSchoolByIdAction(schoolId);
      if (response.status === 'success' && response.data) {
        setSchoolDetails(response.data);
      } else {
        const message = response.status === 'error' ? (Array.isArray(response.message) ? response.message.join(', ') : response.message) : 'Failed to fetch school details.';
        setApiMessage({ type: 'error', message: message || 'Failed to fetch school details.' });
        setSchoolDetails(null);
      }
    } catch (error: any) {
      setApiMessage({ type: 'error', message: error.message || 'Error fetching school details.' });
      setSchoolDetails(null);
    } finally {
      setIsLoadingSchool(false);
    }
  }, [schoolId, status]);

  useEffect(() => {
    fetchSchoolDetails();
  }, [fetchSchoolDetails]);

  // Handlers for Edit School Modal
  const handleOpenEditModal = () => {
    if (schoolDetails) {
      setIsEditModalOpen(true);
    }
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
  };

  const handleEditSuccess = (_updatedSchool: ISchoolResponse | {id: string, name: string}) => {
    fetchSchoolDetails(); // Refresh data
    setApiMessage({ type: 'success', message: 'School details updated successfully!' });
    setIsEditModalOpen(false);
  };

  const handleEditError = (message: string) => {
    setApiMessage({ type: 'error', message: `Failed to update school: ${message}` });
    // Keep modal open or handle as per UX requirements
  };

  // Handler for school deletion
  const handleDeleteSchool = async (schoolId: string) => {
    try {
      const response = await handleDeleteSchoolAction(schoolId);
      if (response.status === 'success') {
        // Show success message and navigate back to school list
        setApiMessage({ type: 'success', message: 'School deleted successfully!' });
        // Short delay to show the success message before navigating
        setTimeout(() => {
          router.push('/school-management');
        }, 1500);
        // return true; // Removed to match Promise<void>
      } else {
        // Show error message
        const message = response.status === 'error' ? 
          (Array.isArray(response.message) ? response.message.join(', ') : response.message) : 
          'Failed to delete school.';
        setApiMessage({ type: 'error', message: message || 'Failed to delete school.' });
        // return false; // Removed to match Promise<void>
      }
    } catch (error: any) {
      setApiMessage({ type: 'error', message: error.message || 'Error deleting school.' });
      // return false; // Removed to match Promise<void>
    }
  };

  // Examination Format handlers are now handled by the ExaminationFormatManager component

  // Render states
  if (status === 'loading' || (isLoadingSchool && !schoolDetails)) {
    return (
      <DashboardTemplate sidebarItems={[]} userMenuDropdownProps={{}} schoolInfo={null}>
        <div className="p-8 flex flex-col items-center justify-center min-h-[60vh]">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading School Details</h2>
          <p className="text-gray-500 mt-2">Please wait while we fetch the information...</p>
        </div>
      </DashboardTemplate>
    );
  }

  if (status === 'unauthenticated') {
    router.push('/auth/sign-in'); // Redirect to login
    return null;
  }

  if (!schoolDetails && !isLoadingSchool) {
     return (
      <DashboardTemplate sidebarItems={[]} userMenuDropdownProps={{}} schoolInfo={null}>
        <div className="p-8 max-w-2xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <div className="flex items-center justify-center text-red-500 bg-red-50 w-16 h-16 rounded-full mx-auto mb-4">
              <Info size={32} />
            </div>
            <h2 className="text-2xl font-bold text-center text-gray-800 mb-4">School Not Found</h2>
            <AlertMessage type="error" message={apiMessage?.message || "The school you're looking for could not be found or failed to load."} />
            <div className="flex justify-center mt-6">
              <Button 
                variant="outline" 
                onClick={() => router.back()} 
                className="flex items-center gap-2"
              >
                <ArrowLeft size={16} />
                Return to School List
              </Button>
            </div>
          </div>
        </div>
      </DashboardTemplate>
    );
  }


  const header = (
    <ListingHeader
      title="School Management"
      subtitle="View and manage school details and resources"
      buttonProps={{
        label: "Back to Schools",
        variant: "outline",
        href: "/school-management",
        className: "flex w-fit items-center gap-1.5",
        iconProps: {
          variant: "arrow-left",
          size: 4 // Reduced from 16 to 4 (16px ÷ themeSpacingValue of 4)
        }
      }}
    />
  );

  const examinationFormatSection = (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
      <div className="p-6">
        {apiMessage && (
          <div className="mb-6">
            <AlertMessage type={apiMessage.type} message={apiMessage.message} />
          </div>
        )}
        
        {/* Using the new ExaminationFormatManager component */}
        <ExaminationFormatManager
          schoolId={schoolId}
          schoolName={schoolDetails?.name}
          onDelete={async () => {
            await deleteExaminationFormatAction(schoolId);
            fetchSchoolDetails();
          }}
          refreshTrigger={refreshTrigger}
          metadata={schoolDetails?.examinationFormat}
        />
      </div>
    </div>
  );

  const content = (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {schoolDetails && (
        <SchoolDetailCard 
          school={schoolDetails} 
          onEdit={handleOpenEditModal}
          onDelete={handleDeleteSchool}
          // Re-add permission check if it was there originally, or adjust as needed
          userRole={session?.user?.role as EUserRole} 
        />
      )}
      {examinationFormatSection}
    </div>
  );

  return (
    <DashboardTemplate sidebarItems={[]} userMenuDropdownProps={{}} schoolInfo={null}>
      <DetailTemplate header={header} content={content} />
      {/* Edit School Modal */}
      {schoolDetails && (
        <SchoolModal
          isOpen={isEditModalOpen}
          onClose={handleCloseEditModal}
          schoolData={schoolDetails} // Pass current school data for editing
          onSuccess={handleEditSuccess} // Handle successful update
          onError={handleEditError} // Handle error during update
        />
      )}
    </DashboardTemplate>
  );
}
